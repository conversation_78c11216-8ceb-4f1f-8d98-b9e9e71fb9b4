[gosec] 2025/08/04 14:09:19 Including rules: default
[gosec] 2025/08/04 14:09:19 Excluding rules: default
[gosec] 2025/08/04 14:09:19 Including analyzers: default
[gosec] 2025/08/04 14:09:19 Excluding analyzers: default
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/cmd/signalsd
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/internal/server/schemas
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/internal/auth
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/internal/version
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/internal/server/handlers
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/internal/apperrors
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/docs
[gosec] 2025/08/04 14:09:19 Import directory: /Users/<USER>/dev/signalsd/app/test/integration
[gosec] 2025/08/04 14:09:19 Checking package: schemas
[gosec] 2025/08/04 14:09:19 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/schemas/schemas.go
[gosec] 2025/08/04 14:09:20 Checking package: signalsd
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/config.go
[gosec] 2025/08/04 14:09:20 Import directory: /Users/<USER>/dev/signalsd/app/internal/server/isns
[gosec] 2025/08/04 14:09:20 Checking package: version
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/internal/version/version.go
[gosec] 2025/08/04 14:09:20 Import directory: /Users/<USER>/dev/signalsd/app/internal/server
[gosec] 2025/08/04 14:09:20 Import directory: /Users/<USER>/dev/signalsd/app/internal/server/responses
[gosec] 2025/08/04 14:09:20 Checking package: main
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/cmd/signalsd/main.go
[gosec] 2025/08/04 14:09:20 Checking package: apperrors
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/internal/apperrors/error_codes.go
[gosec] 2025/08/04 14:09:20 Import directory: /Users/<USER>/dev/signalsd/app/internal/server/utils
[gosec] 2025/08/04 14:09:20 Import directory: /Users/<USER>/dev/signalsd/app/internal/database
[gosec] 2025/08/04 14:09:20 Checking package: auth
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/internal/auth/auth.go
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/internal/auth/context.go
[gosec] 2025/08/04 14:09:20 Checking file: /Users/<USER>/dev/signalsd/app/internal/auth/middleware.go
[gosec] 2025/08/04 14:09:21 Checking package: docs
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/docs/docs.go
[gosec] 2025/08/04 14:09:21 Import directory: /Users/<USER>/dev/signalsd/app/internal/logger
[gosec] 2025/08/04 14:09:21 Checking package: handlers
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/admin.go
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/isn.go
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/isn_accounts.go
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/login.go
[gosec] 2025/08/04 14:09:21 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/service_accounts.go
[gosec] 2025/08/04 14:09:22 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/signal_batches.go
[gosec] 2025/08/04 14:09:22 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/signal_types.go
[gosec] 2025/08/04 14:09:22 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/signals.go
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/tokens.go
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/users.go
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/handlers/webhooks.go
[gosec] 2025/08/04 14:09:23 Checking package: isns
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/isns/cache.go
[gosec] 2025/08/04 14:09:23 Checking package: responses
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/responses/response.go
[gosec] 2025/08/04 14:09:23 Checking package: server
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/middleware.go
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/server.go
[gosec] 2025/08/04 14:09:23 Checking package: utils
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/server/utils/utils.go
[gosec] 2025/08/04 14:09:23 Checking package: database
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/accounts.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/db.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/health.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/isn.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/isn_accounts.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/models.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/refresh_tokens.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/reset.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/service_accounts.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/signal_batches.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/signal_processing_failures.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/signal_types.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/signals.sql.go
[gosec] 2025/08/04 14:09:23 Ignoring generated file: /Users/<USER>/dev/signalsd/app/internal/database/users.sql.go
[gosec] 2025/08/04 14:09:23 Checking package: logger
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/logger/logger.go
[gosec] 2025/08/04 14:09:23 Checking file: /Users/<USER>/dev/signalsd/app/internal/logger/middleware.go
Results:


[1;36mSummary:[0m
  Gosec  : dev
  Files  : 27
  Lines  : 10327
  Nosec  : 1
  Issues : [1;32m0[0m

